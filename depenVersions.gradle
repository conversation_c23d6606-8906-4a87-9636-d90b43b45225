ext {


    // 由于小米包名和superhexa 不一致，在sdk 调用上，有可能要区分一些常量，故把包名提到这里，方便其他地方区分
    consAboutApp = [
            customApplicationId: 'com.xiaomi.superhexa',
    ]
    // 判断是否小米的包
    isXiaomiApp = consAboutApp.customApplicationId.contains("com.xiaomi.superhexa")

    android = [
            applicationId    : 'com.superhexa.supervision',
            compileSdkVersion: 35,
            buildToolsVersion: "32.0.0",
            minSdkVersion    : 29,
            targetSdkVersion : 31,
            javaVersion      : JavaVersion.VERSION_17,
            jvmToolchainVersion : 17
    ]

    versions = [
            kotlin_version        : '1.8.0',
            coroutine_version     : '1.6.3',
            coroutine_test_version: '1.6.2',
            gradletoolsVersion    : '8.0.0',
            exo_version           : '2.19.1',
            nav_version           : '2.3.5',
            lifecycle_version     : '2.4.1',
            kodein_version        : '6.5.5',
            retrofit_version      : '2.9.0',
            stetho_version        : '1.5.1',
            constraintlayout      : '2.1.0',
            glide                 : '4.16.0',
            delivery_version      : '2.0.1',
            okhttp3_version       : '4.9.1',
            lifecycle_runtime_ktx : '2.2.0',
            lifecycle             : '2.3.1',
            protobuf              : '3.9.2',
            objectboxVersion      : '4.3.0',
            work_version          : '2.7.1',
            fragment_version      : '1.3.6',
            fragivity_version     : '0.3.1',
            arouter_version       : '1.5.2',
            immersionbar          : '3.2.2',
            lifecycleVersion      : '2.4.1',
            swipeVersion          : '1.0.0',
            gsonVersion           : '2.8.6',
            animationVersion      : '2.5.1',
            xiaomiAccountVersion  : '3.5.1',
            compose_version       : '1.4.1',
            gsyvideo_version      : '8.1.5-jitpack',
            rxjava                : '2.2.19',
            rxandroid             : '2.1.0',
            ble                   : '2.7.2',
            guava                 : '21.0',
            mockito               : '4.0.0',
            mockk_version         : '1.10.0',
            robolectric           : '4.6.1',
            logback               : '1.4.7',
            junit_ktx             : '1.1.3',
            test_core_ktx         : '1.4.0',
            androidx_arch_core_ktx: '2.1.0',
            google_auth           : '20.3.0',
            hamcrestVersion       : '1.3',
            androidXTestVersion   : '1.5.0',
            espressoVersion       : '3.4.0',
            exoPlayerVersion      : '1.1.1',
            compose_alipay_version: '1.7.1',
            accompanistVersion    : '0.30.1'
    ]

    deps = [
            kotlin_stdlib                       : "org.jetbrains.kotlin:kotlin-stdlib:$versions.kotlin_version",

            kotlin_stdlib_jdk8                  : "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$versions.kotlin_version",
            kotlin_reflect                      : "org.jetbrains.kotlin:kotlin-reflect:$versions.kotlin_version",
            // https://github.com/Kotlin/kotlinx.coroutines
            kotlinx_coroutines_core             : "org.jetbrains.kotlinx:kotlinx-coroutines-core:$versions.coroutine_version",
            kotlinx_coroutines_core_jvm         : "org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:$versions.coroutine_version",
            kotlinx_coroutines_test             : "org.jetbrains.kotlinx:kotlinx-coroutines-test:$versions.coroutine_test_version",

            // https://developer.android.com/jetpack/androidx/releases/lifecycle
            lifecycle_livedata_core_ktx         : "androidx.lifecycle:lifecycle-livedata-core-ktx:$versions.lifecycleVersion",
            lifecycle_livedata_ktx              : "androidx.lifecycle:lifecycle-livedata-ktx:$versions.lifecycleVersion",
            lifecycle_viewmodel_ktx             : "androidx.lifecycle:lifecycle-viewmodel-ktx:$versions.lifecycleVersion",
            lifecycle_runtime_ktx               : "androidx.lifecycle:lifecycle-runtime-ktx:$versions.lifecycleVersion",

            androidx_core                       : "androidx.core:core:1.8.0",
            androidx_core_core_ktx              : "androidx.core:core-ktx:1.8.0",
            androidx_appcompat                  : "androidx.appcompat:appcompat:1.3.1",
            androidx_arch_core_ktx              : "androidx.arch.core:core-testing:$versions.androidx_arch_core_ktx",

            //https://github.com/coil-kt/coil#jetpack-compose
            io_coil_compose                     : "io.coil-kt:coil-compose:2.4.0",
            material                            : "com.google.android.material:material:1.4.0",
            constraintlayout                    : "androidx.constraintlayout:constraintlayout:$versions.constraintlayout",
            swiperefreshlayout                  : "androidx.swiperefreshlayout:swiperefreshlayout:$versions.swipeVersion",

            androidx_annotation                 : "androidx.annotation:annotation:1.1.0",
            //https://developer.android.com/guide/playcore?hl=zh-cn
            googleplay_feature_delivery         : "com.google.android.play:feature-delivery:$versions.delivery_version",
            googleplay_feature_delivery_ktx     : "com.google.android.play:feature-delivery-ktx:$versions.delivery_version",

            exoplayer                           : "com.google.android.exoplayer:exoplayer:$versions.exo_version",
            exoplayer_ui                        : "com.google.android.exoplayer:exoplayer-ui:$versions.exo_version",


            jiaozivideoplayer                   : "cn.jzvd:jiaozivideoplayer:7.7.0",


            gsyVideoPlayer                      : "com.github.CarGuo.GSYVideoPlayer:gsyVideoPlayer:$versions.gsyvideo_version",
            gsyVideoPlayer_java                 : "com.github.CarGuo.GSYVideoPlayer:gsyVideoPlayer-java:$versions.gsyvideo_version",
            gsyVideoPlayer_exo_player2          : "com.github.CarGuo.GSYVideoPlayer:gsyVideoPlayer-exo_player2:$versions.gsyvideo_version",



            lottie                              : 'com.airbnb.android:lottie:5.2.0',
            lottie_compose                      : 'com.airbnb.android:lottie-compose:5.2.0',

            lifecycle_compiler                  : "androidx.lifecycle:lifecycle-compiler:$versions.lifecycle_version",
            lifecycle_viewmodel_savedstate      : "androidx.lifecycle:lifecycle-viewmodel-savedstate:$versions.lifecycle_version",
            lifecycle_extensions                : "android.arch.lifecycle:extensions:$versions.lifecycle_version",
            lifecycle_runtime_ktx               : "androidx.lifecycle:lifecycle-runtime-ktx:$versions.lifecycle_runtime_ktx",
            lifecycle_common                    : "androidx.lifecycle:lifecycle-common-java8:$versions.lifecycle",



            navigation_fragment_ktx             : "androidx.navigation:navigation-fragment-ktx:$versions.nav_version",
            navigation_ui_ktx                   : "androidx.navigation:navigation-ui-ktx:$versions.nav_version",
            navigation_dynamic_features_fragment: "androidx.navigation:navigation-dynamic-features-fragment:$versions.nav_version",



            kodein_di_generic_jvm               : "org.kodein.di:kodein-di-generic-jvm:$versions.kodein_version",
            kodein_di_core                      : "org.kodein.di:kodein-di-framework-android-core:$versions.kodein_version",
            kodein_di_support                   : "org.kodein.di:kodein-di-framework-android-support:$versions.kodein_version",
            kodein_di_androidx                  : "org.kodein.di:kodein-di-framework-android-x:$versions.kodein_version",


            okhttp3                             : "com.squareup.okhttp3:okhttp:$versions.okhttp3_version",
            retrofit2                           : "com.squareup.retrofit2:retrofit:$versions.retrofit_version",
            retrofit_convert_gson               : "com.squareup.retrofit2:converter-gson:$versions.retrofit_version",
            retrofit_convert_moshi              : "com.squareup.retrofit2:converter-moshi:$versions.retrofit_version",
            retrofit_converter_scalars          : "com.squareup.retrofit2:converter-scalars:$versions.retrofit_version",
            gson                                : "com.google.code.gson:gson:$versions.gsonVersion",

            okhttp_logging_interceptor          : "com.squareup.okhttp3:logging-interceptor:$versions.okhttp3_version",

            stetho                              : "com.facebook.stetho:stetho:$versions.stetho_version",
            stetho_okhttp3                      : "com.facebook.stetho:stetho-okhttp3:$versions.stetho_version",

            timber                              : "com.jakewharton.timber:timber:5.0.1",

            // glide 支持 webpdecoder
            webpdecoder                         : "com.github.zjupure:webpdecoder:2.6.$versions.glide",
            glide                               : "com.github.bumptech.glide:glide:$versions.glide",
            glide_compiler                      : "com.github.bumptech.glide:compiler:$versions.glide",
            bugly_upgrade                       : "com.tencent.bugly:crashreport_upgrade:1.6.1",
            bugly_nativecrashreport             : "com.tencent.bugly:nativecrashreport:3.9.1",

            slf4j_api                           : "org.slf4j:slf4j-api:1.7.25",
            logback_android                     : "com.github.tony19:logback-android:2.0.1",
            // 单元测试需要，替代日志组件，日志组件在单测环境不好mock
            logback                             : "ch.qos.logback:logback-classic:$versions.logback",

            junit                               : "junit:junit:4.13.2",
            archunit_junit4                     : "com.tngtech.archunit:archunit-junit4:0.23.1",

            mockk                               : "io.mockk:mockk:$versions.mockk_version",
            mockk_android                       : "io.mockk:mockk-android:$versions.mockk_version",
            mockk_agent_jvm                     : "io.mockk:mockk-agent-jvm:$versions.mockk_version",
            androidx_legacy_support_v4          : "androidx.legacy:legacy-support-v4:1.0.0",
            spannedgridlayoutmanager            : "com.arasthel:spannedgridlayoutmanager:3.0.2",
            leakcanary_android                  : "com.squareup.leakcanary:leakcanary-android:2.9.1",
            protobuf_java                       : "com.google.protobuf:protobuf-java:$versions.protobuf",
            protoc                              : "com.google.protobuf:protoc:$versions.protobuf",
            // 服务器端自动根据protobuf文件生成的bean，埋点需求中，protobuf文件由服务端自动修改生成
            protobuf_auto_produce_bean          : "com.superhexa:cls-meta:1.0.0-SNAPSHOT",
            // 微信出品的替代SharedPreference的高性能持久化数据的东东
            mmkv_static                         : "com.tencent:mmkv-static:1.2.13",
            startup_runtime                     : "androidx.startup:startup-runtime:1.0.0",
            // 文件对象型数据库，贼快
            objectbox_android                   : "io.objectbox:objectbox-android:$versions.objectboxVersion",
            // 测试环境objectbox 浏览器
            objectbox_android_objectbrowser     : "io.objectbox:objectbox-android-objectbrowser:$versions.objectboxVersion",
            // objectbox官方提供的对kotlin的函数扩展
            objectbox_kotlin                    : "io.objectbox:objectbox-kotlin:$versions.objectboxVersion",
            objectbox_ut_mac                    : "io.objectbox:objectbox-macos:$versions.objectboxVersion",
            objectbox_ut_linux                  : "io.objectbox:objectbox-linux:$versions.objectboxVersion",
            objectbox_ut_windows                : "io.objectbox:objectbox-windows:$versions.objectboxVersion",

            // WorkManager 支持 Kotlin + coroutines 目前没有加入对java的支持  https://developer.android.com/topic/libraries/architecture/workmanager/basics
            workManager_runtime_ktx             : "androidx.work:work-runtime-ktx:$versions.work_version",
            // WorkManager 支持多进程
            workManager_multiprocess            : "androidx.work:work-multiprocess:$versions.work_version",

            //支持 kotlin 扩展的 的fragment  https://developer.android.com/kotlin/ktx#fragment
            fragment_ktx                        : "androidx.fragment:fragment-ktx:$versions.fragment_version",
            fragment_testing                    : "androidx.fragment:fragment-testing:$versions.fragment_version",

            // fragivity 对navigation的再封装，灵活好用 https://juejin.cn/post/6918693610359619592
            fragivity                           : "com.github.vitaviva.fragivity:core:$versions.fragivity_version",
            // fragivity DeepLink
            fragivity_processor                 : "com.github.vitaviva.fragivity:processor:$versions.fragivity_version",

            // 替换成最新版本, 需要注意的是api
            // 要与compiler匹配使用，均使用最新版可以保证兼容 新版已经包含 com.alibaba:arouter-annotation
            // 无需单独再加了
            arouter_api                         : "com.alibaba:arouter-api:$versions.arouter_version",
            arouter_compiler                    : "com.alibaba:arouter-compiler:$versions.arouter_version",

            XXPermissions                       : "com.github.getActivity:XXPermissions:16.6",
            MPAndroidChart                      : "com.github.PhilJay:MPAndroidChart:v3.1.0",
            // 基础的recycleviewHelper
            BaseRecyclerViewAdapterHelper       : "com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.4",
            // 突破android P(28)之后限制反射的库 https://github.com/LSPosed/AndroidHiddenApiBypass
            hiddenapibypass                     : "org.lsposed.hiddenapibypass:hiddenapibypass:4.3",
            // recyclerView
            recyclerview                        : "androidx.recyclerview:recyclerview:1.0.0",
            roundCornerProgressBar              : "com.akexorcist:round-corner-progress-bar:2.1.2",
            // eventbus
            eventbus                            : "org.greenrobot:eventbus:3.2.0",
            liveEventBus                        : "io.github.jeremyliao:live-event-bus-x:1.8.0",

            // 蓝牙连接模块相关
            // 基础蓝牙支持库  https://github.com/NordicSemiconductor/Android-BLE-Library/issues/217
            ble                                 : "no.nordicsemi.android:ble:$versions.ble",
            // 上面的扩展 ，支持SIG characteristics
            ble_common                          : "no.nordicsemi.android:ble-common:$versions.ble",
            ble_ktx                             : "no.nordicsemi.android:ble-ktx:$versions.ble",
            ble_livedata                        : "no.nordicsemi.android:ble-livedata:$versions.ble",
            // 解决了很多兼容问题的对Ble 的扫描库，https://github.com/NordicSemiconductor/Android-Scanner-Compat-Library
            ble_scanner                         : "no.nordicsemi.android.support.v18:scanner:1.5.1",

            tink_android                        : "com.google.crypto.tink:tink-android:1.6.1",
            bcprov_jdk15on                      : "org.bouncycastle:bcprov-jdk15on:1.59",
            pcm_decoder                         : "com.github.lincollincol:PCM-Decoder:1.0",

            // ve core模块相关
            // 可以使用谷歌模型
            segmentation_selfie                 : "com.google.mlkit:segmentation-selfie:16.0.0-beta2",
            //谷歌 人脸识别、分割 https://developers.google.com/ml-kit
            face_detection                      : 'com.google.mlkit:face-detection:16.1.2',
            localbroadcastmanager               : 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0',
            apng                                : "com.github.penfeizhou.android.animation:apng:$versions.animationVersion",
            glide_plugin                        : "com.github.penfeizhou.android.animation:glide-plugin:$versions.animationVersion",
            frameanimation                      : "com.github.penfeizhou.android.animation:frameanimation:$versions.animationVersion",

            // 阿里云 oss
            oss_android_sdk                     : "com.aliyun.dpa:oss-android-sdk:2.9.9",

            // 小米账号 授权
//            xiaomi_author_sdk                   : "com.xiaomi.account:oauth-android:$versions.xiaomiAccountVersion",
//            xiaomi_passport_sdk                 : "com.xiaomi.account:passportsdk-diagnosis:$versions.xiaomiAccountVersion",

            // 小米帐户Passport SDK
//            passportsdk_ui                      : "com.xiaomi.account:passportsdk-client-ui:${versions.passportsdk}",
            rxjava                              : "io.reactivex.rxjava2:rxjava:${versions.rxjava}",
            rxandroid                           : "io.reactivex.rxjava2:rxandroid:${versions.rxandroid}",


            /**
             * 新版小米账号sdk
             * 在新版本上接入 参考 https://xiaomi.f.mioffice.cn/wiki/wikk4Cg5Vtzzo8bte4hofJ08P2g#V78eYi
             * oauth-android 在新版本中被替换为 passportsdk-oauth
             * passportsdk-diagnosis  passportsdk-client-ui  在新版本中被替换为 passportsdk-account-sso
             * "com.xiaomi.account:passportsdk-account-sso:5.0.0.bugfix"
             "com.xiaomi.account:passportsdk-account-oauth:5.0.0"
             */
            xiaomi_passportsdk_account_lib      : "com.xiaomi.account:passportsdk-account-lib:5.2.0.release.39",
            xiaomi_passportsdk_account_sso      : "com.xiaomi.account:passportsdk-account-sso:5.2.0.release.47",
            xiaomi_passportsdk_account_oauth    : "com.xiaomi.account:passportsdk-account-oauth:5.0.0",



            // composeUI
            compose_ui                          : "androidx.compose.ui:ui:$versions.compose_version",
            compose_ui_graphics                 : "androidx.compose.ui:ui-graphics-android:$versions.compose_version",
            compose_ui_util                     : "androidx.compose.ui:ui-util:$versions.compose_version",
            compose_material                    : "androidx.compose.material:material:$versions.compose_version",
            compose_ui_tooling                  : "androidx.compose.ui:ui-tooling:$versions.compose_version",
            compose_ui_test_junit4              : "androidx.compose.ui:ui-test-junit4:$versions.compose_version",
            compose_ui_test_manifest            : "androidx.compose.ui:ui-test-manifest:$versions.compose_version",
            compose_runtime_livedata            : "androidx.compose.runtime:runtime-livedata:$versions.compose_version",
            compose_ui_constraintlayout         : "androidx.constraintlayout:constraintlayout-compose:1.0.0",
            compose_activity                    : "androidx.activity:activity-compose:1.5.0",
            compose_lifecycle_runtime_android   : "androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7",
            compose_alipay_runtime              : "androidx.compose.runtime:runtime-android:$versions.compose_alipay_version",
            compose_alipay_ui                   : "androidx.compose.ui:ui:$versions.compose_alipay_version",
            compose_alipay_animation            : "androidx.compose.animation:animation:$versions.compose_alipay_version",
            accompanist_flowlayout              : "com.google.accompanist:accompanist-flowlayout:$versions.accompanistVersion",

            // zoomlayout需要
            zoomlayout_egloo                    : "com.otaliastudios.opengl:egloo:0.6.1",

            immersionbar                        : "com.geyifeng.immersionbar:immersionbar:$versions.immersionbar",
            immersionbar_ktx                    : "com.geyifeng.immersionbar:immersionbar-ktx:$versions.immersionbar",
            toastcompat                         : "me.drakeet.support:toastcompat:1.1.0",

            guava                               : "com.google.guava:guava:$versions.guava",
            objectbox_macos                     : "io.objectbox:objectbox-macos:$versions.objectboxVersion",
            objectbox_linux                     : "io.objectbox:objectbox-linux:$versions.objectboxVersion",
            objectbox_windows                   : "io.objectbox:objectbox-windows:$versions.objectboxVersion",
            mockito                             : "org.mockito:mockito-core:$versions.mockito",
            mockito_android                     : "org.mockito:mockito-android:$versions.mockito",
            mockito_kotlin                      : "org.mockito.kotlin:mockito-kotlin:$versions.mockito",

            mockwebserver                       : "com.squareup.okhttp3:mockwebserver:$versions.okhttp3_version",
            robolectric                         : "org.robolectric:robolectric:$versions.robolectric",
            junit_ktx                           : "androidx.test.ext:junit-ktx:$versions.junit_ktx",
            android_test_core_ktx               : "androidx.test:core-ktx:$versions.test_core_ktx",
            hamcrest_all                        : "org.hamcrest:hamcrest-all:$versions.hamcrestVersion",


            facebook                            : 'com.facebook.android:facebook-android-sdk:[8,9)',
            google_play_services_auth           : "com.google.android.gms:play-services-auth:$versions.google_auth",

            androidx_test_core                  : "androidx.test:core:$versions.test_core_ktx",
            androidx_test_junit                 : "androidx.test.ext:junit:$versions.junit_ktx",
            androidx_test_espresso_core         : "androidx.test.espresso:espresso-core:$versions.espressoVersion",

            //  androidX 插桩测试
            androidx_test_runner                : "androidx.test:runner:$versions.androidXTestVersion",
            androidx_test_rules                 : "androidx.test:rules:$versions.androidXTestVersion",
            androidx_test_rules                 : "androidx.test:rules:$versions.androidXTestVersion",
            // 小米埋点sdk
            onetrack                            : "com.xiaomi:onetrack-sdk-noimei:2.1.4",
            // csv
            csv                                 : "com.opencsv:opencsv:5.6",
            webkit                              : "androidx.webkit:webkit:1.6.0",

         // androidx_media3_exoplayer
            androidx_media3_exoplayer           : "androidx.media3:media3-exoplayer:$versions.exoPlayerVersion"
    ]

}
